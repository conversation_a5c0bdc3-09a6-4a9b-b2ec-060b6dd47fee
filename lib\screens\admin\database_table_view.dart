import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart' as excel_lib;
import 'package:file_picker/file_picker.dart';
import 'package:file_saver/file_saver.dart';
import 'dart:convert';
import 'dart:typed_data';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../models/database_table_model.dart';
import '../../utils/responsive_helper.dart';
import 'database_row_editor.dart';

/// واجهة عرض جدول البيانات باستخدام PlutoGrid
///
/// توفر واجهة متقدمة لعرض وتحرير بيانات جدول مع ميزات PlutoGrid
class DatabaseTableView extends StatefulWidget {
  final DatabaseManagementController controller;
  final DatabaseTable table;

  const DatabaseTableView({
    super.key,
    required this.controller,
    required this.table,
  });

  @override
  State<DatabaseTableView> createState() => _DatabaseTableViewState();
}

class _DatabaseTableViewState extends State<DatabaseTableView> {
  final TextEditingController _searchController = TextEditingController();

  // PlutoGrid state manager
  PlutoGridStateManager? _plutoGridStateManager;

  @override
  void initState() {
    super.initState();

    // تحميل بيانات الجدول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.controller.loadTableData();
    });
  }

  /// إنشاء أعمدة PlutoGrid
  List<PlutoColumn> _createPlutoColumns() {
    final columns = <PlutoColumn>[];

    // عمود ترقيم الصفوف
    columns.add(
      PlutoColumn(
        title: '#',
        field: '_row_number',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        width: 60,
        minWidth: 60,
        frozen: PlutoColumnFrozen.start,
        renderer: (rendererContext) {
          final rowIndex = rendererContext.stateManager.refRows.indexOf(rendererContext.row) + 1;
          return Container(
            alignment: Alignment.center,
            child: Text(
              '$rowIndex',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          );
        },
      ),
    );

    // عمود سحب الصفوف
    columns.add(
      PlutoColumn(
        title: '↕',
        field: '_row_drag',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        enableRowDrag: true, // تفعيل سحب الصفوف
        width: 40,
        minWidth: 40,
        frozen: PlutoColumnFrozen.start,
        renderer: (rendererContext) {
          return const Icon(
            Icons.drag_handle,
            color: Colors.grey,
            size: 18,
          );
        },
      ),
    );

    // عمود الإجراءات
    columns.add(
      PlutoColumn(
        title: 'الإجراءات',
        field: '_actions',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        width: 120,
        minWidth: 120,
        frozen: PlutoColumnFrozen.start,
        renderer: (rendererContext) {
          return _buildActionsCell(rendererContext.row.toJson());
        },
      ),
    );

    // أعمدة البيانات
    final visibleColumns = widget.table.columns
        .where((col) => col.isVisibleInList)
        .toList();

    for (final col in visibleColumns) {
      columns.add(_createPlutoColumnFromDatabaseColumn(col));
    }

    return columns;
  }

  /// إنشاء عمود PlutoGrid من عمود قاعدة البيانات
  PlutoColumn _createPlutoColumnFromDatabaseColumn(DatabaseColumn col) {
    PlutoColumnType columnType;

    // تحديد نوع العمود
    switch (col.type) {
      case DatabaseColumnType.integer:
      case DatabaseColumnType.decimal:
      case DatabaseColumnType.real:
        columnType = PlutoColumnType.number();
        break;
      case DatabaseColumnType.date:
        columnType = PlutoColumnType.date();
        break;
      case DatabaseColumnType.datetime:
      case DatabaseColumnType.time:
        columnType = PlutoColumnType.time();
        break;
      case DatabaseColumnType.boolean:
        columnType = PlutoColumnType.select(['true', 'false']);
        break;
      case DatabaseColumnType.enumType:
        if (col.allowedValueNames != null && col.allowedValueNames!.isNotEmpty) {
          columnType = PlutoColumnType.select(col.allowedValueNames!);
        } else {
          columnType = PlutoColumnType.text();
        }
        break;
      default:
        columnType = PlutoColumnType.text();
    }

    return PlutoColumn(
      title: col.displayName ?? col.name,
      field: col.name,
      type: columnType,
      enableEditingMode: col.isEditable && widget.table.isEditable,
      enableSorting: col.isSortable,
      enableColumnDrag: true, // تفعيل سحب الأعمدة
      enableContextMenu: true, // تفعيل قائمة السياق
      enableFilterMenuItem: true, // تفعيل التصفية
      enableAutoEditing: false, // منع التحرير التلقائي
      width: _getColumnWidth(col),
      minWidth: 80,
      renderer: (rendererContext) {
        return _buildPlutoCellContent(col, rendererContext.cell.value);
      },
    );
  }

  /// تحديد عرض العمود
  double _getColumnWidth(DatabaseColumn col) {
    switch (col.type) {
      case DatabaseColumnType.boolean:
        return 100;
      case DatabaseColumnType.date:
        return 120;
      case DatabaseColumnType.datetime:
      case DatabaseColumnType.time:
        return 160;
      case DatabaseColumnType.integer:
        return 100;
      case DatabaseColumnType.decimal:
      case DatabaseColumnType.real:
        return 120;
      case DatabaseColumnType.color:
        return 120;
      case DatabaseColumnType.image:
      case DatabaseColumnType.file:
        return 100;
      default:
        return 150;
    }
  }

  /// بناء محتوى خلية PlutoGrid
  Widget _buildPlutoCellContent(DatabaseColumn column, dynamic value) {
    if (value == null) {
      return const Text('-');
    }

    switch (column.type) {
      case DatabaseColumnType.boolean:
        return Icon(
          value == 1 || value == true || value.toString().toLowerCase() == 'true'
              ? Icons.check_circle
              : Icons.cancel,
          color: value == 1 || value == true || value.toString().toLowerCase() == 'true'
              ? Colors.green
              : Colors.red,
          size: 20,
        );

      case DatabaseColumnType.color:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: _parseColor(value.toString()),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(value.toString()),
          ],
        );

      case DatabaseColumnType.image:
        return value.toString().isNotEmpty
            ? const Icon(Icons.image, color: Colors.blue)
            : const Text('-');

      case DatabaseColumnType.file:
        return value.toString().isNotEmpty
            ? const Icon(Icons.insert_drive_file, color: Colors.blue)
            : const Text('-');

      case DatabaseColumnType.password:
        return const Text('••••••••');

      default:
        return Text(
          value.toString(),
          overflow: TextOverflow.ellipsis,
        );
    }
  }

  /// بناء خلية الإجراءات
  Widget _buildActionsCell(Map<String, dynamic> row) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.table.isEditable)
          IconButton(
            icon: const Icon(Icons.edit, size: 18),
            tooltip: 'تعديل',
            onPressed: () => _showEditRowDialog(row),
          ),
        if (widget.table.isDeletable)
          IconButton(
            icon: const Icon(Icons.delete, size: 18),
            tooltip: 'حذف',
            color: AppColors.error,
            onPressed: () => _showDeleteConfirmation(row),
          ),
      ],
    );
  }

  /// إنشاء صفوف PlutoGrid من بيانات الجدول
  /// تم تعطيل هذه الدالة لصالح Lazy Pagination
  /*
  List<PlutoRow> _createPlutoRows() {
    final rows = <PlutoRow>[];

    for (final rowData in widget.controller.tableData) {
      final cells = <String, PlutoCell>{};

      // خلية ترقيم الصفوف
      cells['_row_number'] = PlutoCell(value: 0); // سيتم تحديثها في renderer

      // خلية سحب الصفوف
      cells['_row_drag'] = PlutoCell(value: '');

      // خلية الإجراءات
      cells['_actions'] = PlutoCell(value: '');

      // خلايا البيانات
      final visibleColumns = widget.table.columns
          .where((col) => col.isVisibleInList)
          .toList();

      for (final col in visibleColumns) {
        final value = rowData[col.name];
        cells[col.name] = PlutoCell(value: value ?? '');
      }

      rows.add(PlutoRow(cells: cells));
    }

    return rows;
  }
  */

  @override
  void didUpdateWidget(DatabaseTableView oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إذا تغير الجدول، مسح البحث السابق
    if (oldWidget.table.name != widget.table.name) {
      _searchController.clear(); // مسح نص البحث السابق
      widget.controller.clearSearch(); // مسح البحث من الكنترولر
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = ResponsiveHelper.isTablet(context) || ResponsiveHelper.isDesktop(context);

    return Obx(() {
      // عرض التخطيط الكامل مع شريط الأدوات في جميع الحالات
      return Column(
        children: [
          // شريط الأدوات - يظهر دائماً
          _buildToolbar(isLargeScreen),

          // عرض البيانات
          Expanded(
            child: _buildDataView(),
          ),
        ],
      );
    });
  }

  /// بناء عرض البيانات
  Widget _buildDataView() {
    if (widget.controller.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (widget.controller.error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: AppColors.error,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.controller.error,
              textAlign: TextAlign.center,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                widget.controller.loadTableData();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    // إذا كانت البيانات فارغة، عرض PlutoGrid فارغ مع رسالة
    if (widget.controller.tableData.isEmpty) {
      return _buildEmptyPlutoGrid();
    }

    // عرض PlutoGrid مع البيانات
    return _buildPlutoGrid();
  }

  Widget _buildToolbar(bool isLargeScreen) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                // عنوان الجدول مع إحصائيات
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            widget.table.displayName,
                            style: AppStyles.headingMedium,
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: widget.controller.tableData.isEmpty
                                  ? Colors.grey.shade200
                                  : AppColors.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${widget.controller.tableData.length} سجل',
                              style: AppStyles.bodySmall.copyWith(
                                color: widget.controller.tableData.isEmpty
                                    ? Colors.grey.shade600
                                    : AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.table.description ?? 'لا يوجد وصف',
                        style: AppStyles.bodyMedium.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),

                // أزرار الإجراءات
                if (widget.table.isCreatable)
                  ElevatedButton.icon(
                    onPressed: _showAddRowDialog,
                    icon: const Icon(Icons.add),
                    label: Text(isLargeScreen ? 'إضافة سجل جديد' : 'إضافة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                const SizedBox(width: 8),
                if (widget.table.isExportable)
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (widget.controller.tableData.isEmpty) {
                        Get.snackbar(
                          'تنبيه',
                          'لا توجد بيانات للتصدير',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.orange,
                          colorText: Colors.white,
                        );
                        return;
                      }

                      switch (value) {
                        case 'csv':
                          _exportToCsv();
                          break;
                        case 'excel':
                          _exportToExcel();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'csv',
                        child: Row(
                          children: [
                            Icon(Icons.table_chart),
                            SizedBox(width: 8),
                            Text('تصدير CSV'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'excel',
                        child: Row(
                          children: [
                            Icon(Icons.grid_on),
                            SizedBox(width: 8),
                            Text('تصدير Excel'),
                          ],
                        ),
                      ),
                    ],
                    child: OutlinedButton.icon(
                      onPressed: null, // سيتم التعامل مع الضغط عبر PopupMenuButton
                      icon: const Icon(Icons.download),
                      label: Text(isLargeScreen ? 'تصدير البيانات' : 'تصدير'),
                    ),
                  ),
                const SizedBox(width: 8),
                if (widget.table.isImportable)
                  OutlinedButton.icon(
                    onPressed: _importFromCsv,
                    icon: const Icon(Icons.upload),
                    label: Text(isLargeScreen ? 'استيراد البيانات' : 'استيراد'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // حقل البحث العام
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'بحث عام في جميع الحقول...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                widget.controller.clearSearch();
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onSubmitted: (value) {
                      debugPrint('تم إرسال البحث: "$value"');
                      if (value.isNotEmpty) {
                        debugPrint('تنفيذ البحث في الجدول: ${widget.table.name}');
                        widget.controller.search(value);
                      } else {
                        debugPrint('مسح البحث');
                        widget.controller.clearSearch();
                      }
                    },
                    onChanged: (value) {
                      // البحث التلقائي أثناء الكتابة (اختياري)
                      if (value.isEmpty) {
                        widget.controller.clearSearch();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),

                // زر البحث
                ElevatedButton(
                  onPressed: () {
                    final searchText = _searchController.text;
                    debugPrint('تم الضغط على زر البحث: "$searchText"');
                    if (searchText.isNotEmpty) {
                      debugPrint('تنفيذ البحث العام في الجدول: ${widget.table.name}');
                      widget.controller.search(searchText);
                    } else {
                      debugPrint('مسح البحث من زر البحث');
                      widget.controller.clearSearch();
                    }
                  },
                  child: const Text('بحث'),
                ),
                const SizedBox(width: 8),

                // زر إزالة التصفية
                if (widget.controller.filterClause.value.isNotEmpty ||
                    widget.controller.searchText.value.isNotEmpty)
                  OutlinedButton.icon(
                    onPressed: () {
                      _searchController.clear();
                      widget.controller.clearFilter();
                      widget.controller.clearSearch();
                    },
                    icon: const Icon(Icons.filter_alt_off),
                    label: const Text('إزالة التصفية'),
                  ),

                // زر تحديث البيانات
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () {
                    widget.controller.loadTableData();
                  },
                  icon: const Icon(Icons.refresh),
                  tooltip: 'تحديث البيانات',
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// بناء PlutoGrid فارغ مع رسالة
  Widget _buildEmptyPlutoGrid() {
    // إنشاء الأعمدة فقط (بدون صفوف)
    final columns = _createPlutoColumns();

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // رأس الجدول
          Container(
            height: 45,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              border: Border(
                bottom: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.table_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  '${widget.table.displayName} - 0 سجل',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // عرض أسماء الأعمدة
          if (columns.isNotEmpty) ...[
            Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: Row(
                children: columns.map((column) {
                  return Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      alignment: Alignment.centerRight,
                      child: Text(
                        column.title,
                        style: AppStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade700,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],

          // رسالة الجدول الفارغ
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.table_rows_outlined,
                        color: Colors.grey.shade400,
                        size: 48,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'لا توجد بيانات في هذا الجدول',
                        style: AppStyles.titleMedium.copyWith(
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        'استخدم شريط الأدوات أعلاه لإضافة البيانات أو استيرادها',
                        textAlign: TextAlign.center,
                        style: AppStyles.bodyMedium.copyWith(
                          color: Colors.grey.shade500,
                        ),
                      ),
                      if (widget.table.isCreatable) ...[
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _showAddRowDialog,
                          icon: const Icon(Icons.add, size: 18),
                          label: const Text('إضافة سجل جديد'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء PlutoGrid مع Lazy Pagination
  Widget _buildPlutoGrid() {
    // إنشاء الأعمدة
    final columns = _createPlutoColumns();

    // بدء بقائمة فارغة - سيتم تحميل البيانات عبر lazy pagination
    final rows = <PlutoRow>[];

    return PlutoGrid(
      columns: columns,
      rows: rows,
      onLoaded: (PlutoGridOnLoadedEvent event) {
        _plutoGridStateManager = event.stateManager;

        // تطبيق إعدادات PlutoGrid المتقدمة
        _plutoGridStateManager?.setConfiguration(
          PlutoGridConfiguration(
            style: const PlutoGridStyleConfig(
              gridBorderRadius: BorderRadius.all(Radius.circular(8)),
              enableGridBorderShadow: true,
              enableColumnBorderVertical: true,
              enableColumnBorderHorizontal: true,
              enableCellBorderVertical: true,
              enableCellBorderHorizontal: true,
              enableRowColorAnimation: true,
              gridBorderColor: Colors.grey,
              activatedBorderColor: Colors.blue,
              inactivatedBorderColor: Colors.grey,
              borderColor: Colors.grey,
              rowHeight: 45,
              columnHeight: 45,
              columnFilterHeight: 35,
              cellTextStyle: TextStyle(fontSize: 14),
              columnTextStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              iconSize: 18,
              defaultColumnTitlePadding: EdgeInsets.symmetric(horizontal: 8),
              defaultCellPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            ),
            columnSize: const PlutoGridColumnSizeConfig(
              autoSizeMode: PlutoAutoSizeMode.scale,
              resizeMode: PlutoResizeMode.pushAndPull,
              restoreAutoSizeAfterHideColumn: true,
              restoreAutoSizeAfterFrozenColumn: true,
              restoreAutoSizeAfterMoveColumn: true,
              restoreAutoSizeAfterInsertColumn: true,
              restoreAutoSizeAfterRemoveColumn: true,
            ),
            scrollbar: const PlutoGridScrollbarConfig(
              draggableScrollbar: true,
              isAlwaysShown: true,
              scrollbarThickness: 8,
              scrollbarThicknessWhileDragging: 10,
              scrollbarRadius: Radius.circular(4),
              scrollbarRadiusWhileDragging: Radius.circular(6),
            ),
            columnFilter: PlutoGridColumnFilterConfig(
              filters: const [
                PlutoFilterTypeContains(),
                PlutoFilterTypeEquals(),
                PlutoFilterTypeStartsWith(),
                PlutoFilterTypeEndsWith(),
                PlutoFilterTypeGreaterThan(),
                PlutoFilterTypeGreaterThanOrEqualTo(),
                PlutoFilterTypeLessThan(),
                PlutoFilterTypeLessThanOrEqualTo(),
              ],
            ),
            enterKeyAction: PlutoGridEnterKeyAction.editingAndMoveDown,
            enableMoveDownAfterSelecting: true,
            enableMoveHorizontalInEditing: true,
            localeText: PlutoGridLocaleText.arabic(),
          ),
        );

        // تفعيل التصفية
        _plutoGridStateManager?.setShowColumnFilter(true);
      },
      onChanged: (PlutoGridOnChangedEvent event) {
        // التعامل مع تغيير البيانات
        debugPrint('تم تغيير الخلية: ${event.column.field} = ${event.value}');
      },
      onSorted: (PlutoGridOnSortedEvent event) {
        // التعامل مع الترتيب
        final columnName = event.column.field;
        final isAscending = event.column.sort == PlutoColumnSort.ascending;
        widget.controller.changeOrder(columnName, isAscending);
      },
      onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
        // فتح نافذة التحرير عند النقر المزدوج
        if (widget.table.isEditable) {
          final rowData = event.row.toJson();
          _showEditRowDialog(rowData);
        }
      },
      onRowsMoved: (PlutoGridOnRowsMovedEvent event) {
        // التعامل مع سحب الصفوف
        debugPrint('تم نقل الصفوف: ${event.idx} صف');
        // يمكن إضافة منطق لحفظ ترتيب الصفوف الجديد
      },
      createHeader: (stateManager) {
        return Obx(() {
          return Container(
            height: 45,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.table_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  '${widget.table.displayName} - ${widget.controller.tableData.length} سجل',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
        });
      },
      // إضافة Lazy Pagination
      createFooter: (stateManager) {
        return PlutoLazyPagination(
          initialPage: 1,
          initialFetch: true,
          fetchWithSorting: true,
          fetchWithFiltering: true,
          pageSizeToMove: 5,
          fetch: _fetchDataForPagination,
          stateManager: stateManager,
        );
      },
    );
  }

  /// دالة تحميل البيانات للـ Lazy Pagination
  Future<PlutoLazyPaginationResponse> _fetchDataForPagination(
    PlutoLazyPaginationRequest request,
  ) async {
    try {
      debugPrint('تحميل الصفحة: ${request.page}');

      // تحديث معاملات الكنترولر
      widget.controller.goToPage(request.page);

      // معالجة الفلاتر
      Map<String, dynamic>? filters;
      if (request.filterRows.isNotEmpty) {
        filters = {};
        for (final filterRow in request.filterRows) {
          for (final cell in filterRow.cells.entries) {
            final columnField = cell.key;
            final cellValue = cell.value.value;

            if (cellValue != null && cellValue.toString().isNotEmpty) {
              filters[columnField] = cellValue.toString();
            }
          }
        }
      }

      // معالجة الترتيب
      if (request.sortColumn != null && !request.sortColumn!.sort.isNone) {
        final isAscending = request.sortColumn!.sort == PlutoColumnSort.ascending;
        widget.controller.changeOrder(request.sortColumn!.field, isAscending);
      }

      // تطبيق الفلاتر إذا وجدت
      if (filters != null) {
        widget.controller.applyFilters(filters);
      }

      // انتظار تحميل البيانات
      await widget.controller.loadTableData();

      // تحويل البيانات إلى PlutoRows
      final rows = widget.controller.tableData.map((rowData) {
        final cells = <String, PlutoCell>{};

        // خلية ترقيم الصفوف
        cells['_row_number'] = PlutoCell(value: 0);

        // خلية سحب الصفوف
        cells['_row_drag'] = PlutoCell(value: '');

        // خلية الإجراءات
        cells['_actions'] = PlutoCell(value: '');

        // خلايا البيانات
        final visibleColumns = widget.table.columns
            .where((col) => col.isVisibleInList)
            .toList();

        for (final col in visibleColumns) {
          final value = rowData[col.name];
          cells[col.name] = PlutoCell(value: value ?? '');
        }

        return PlutoRow(cells: cells);
      }).toList();

      // حساب إجمالي الصفحات (تقدير بناءً على حجم الصفحة)
      final totalPages = (widget.controller.tableData.length / widget.controller.pageSize).ceil();
      final adjustedTotalPages = totalPages > 0 ? totalPages : 1;

      return PlutoLazyPaginationResponse(
        totalPage: adjustedTotalPages,
        rows: rows,
      );

    } catch (e) {
      debugPrint('خطأ في تحميل البيانات للـ pagination: $e');
      return PlutoLazyPaginationResponse(
        totalPage: 1,
        rows: [],
      );
    }
  }

  void _showAddRowDialog() {
    // تنفيذ إضافة سجل جديد
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('إضافة سجل جديد'),
          content: SizedBox(
            width: 500,
            child: DatabaseRowEditor(
              table: widget.table,
              onSave: (row) {
                Navigator.of(context).pop();
                widget.controller.createRecord(row).then((_) {
                  // إعادة تحميل البيانات لضمان تحديث PlutoGrid
                  widget.controller.loadTableData();
                });
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showEditRowDialog(Map<String, dynamic> row) {
    // تنفيذ تعديل سجل
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل سجل'),
          content: SizedBox(
            width: 500,
            child: DatabaseRowEditor(
              table: widget.table,
              initialValues: row,
              onSave: (updatedRow) {
                Navigator.of(context).pop();

                // البحث عن العمود المفتاح الأساسي
                final primaryKeyColumn = widget.table.columns.firstWhere(
                  (col) => col.isPrimaryKey,
                  orElse: () => widget.table.columns.first,
                );

                widget.controller.updateRow(updatedRow, primaryKeyColumn.name).then((_) {
                  // إعادة تحميل البيانات لضمان تحديث PlutoGrid
                  widget.controller.loadTableData();
                });
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> row) {
    // البحث عن العمود المفتاح الأساسي
    final primaryKeyColumn = widget.table.columns.firstWhere(
      (col) => col.isPrimaryKey,
      orElse: () => widget.table.columns.first,
    );

    final primaryKeyValue = row[primaryKeyColumn.name];

    // البحث عن عمود العرض
    final displayColumn = widget.table.columns.firstWhere(
      (col) => col.isVisibleInList && !col.isPrimaryKey,
      orElse: () => primaryKeyColumn,
    );

    final displayValue = row[displayColumn.name];

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف السجل "${displayValue ?? primaryKeyValue}"؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.controller.deleteRecord(primaryKeyValue).then((_) {
                  // إعادة تحميل البيانات لضمان تحديث PlutoGrid
                  widget.controller.loadTableData();
                });
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// تصدير البيانات إلى CSV باستخدام مكتبة CSV
  void _exportToCsv() async {
    try {
      if (_plutoGridStateManager == null) {
        Get.snackbar(
          'خطأ',
          'لا يمكن تصدير البيانات. الجدول غير جاهز.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // تحضير البيانات للتصدير
      final List<List<String>> csvData = [];

      // إضافة رؤوس الأعمدة (باستثناء الأعمدة الخاصة)
      final headers = <String>[];
      final visibleColumns = widget.table.columns.where((col) => col.isVisibleInList).toList();
      for (final column in visibleColumns) {
        headers.add(column.displayName ?? column.name);
      }
      csvData.add(headers);

      // إضافة بيانات الصفوف
      for (final rowData in widget.controller.tableData) {
        final row = <String>[];
        for (final column in visibleColumns) {
          final value = rowData[column.name];
          row.add(value?.toString() ?? '');
        }
        csvData.add(row);
      }

      // تحويل البيانات إلى CSV
      final csvString = const ListToCsvConverter().convert(csvData);
      final bytes = utf8.encode(csvString);

      // حفظ الملف
      final fileName = '${widget.table.name}_${DateTime.now().millisecondsSinceEpoch}.csv';
      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: Uint8List.fromList(bytes),
        ext: 'csv',
        mimeType: MimeType.csv,
      );

      // إغلاق مؤشر التحميل
      Get.back();

      Get.snackbar(
        'تصدير البيانات',
        'تم تصدير ${widget.controller.tableData.length} سجل إلى ملف CSV بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تصدير البيانات إلى Excel
  void _exportToExcel() async {
    try {
      if (_plutoGridStateManager == null) {
        Get.snackbar(
          'خطأ',
          'لا يمكن تصدير البيانات. الجدول غير جاهز.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // إنشاء ملف Excel جديد
      final excel_lib.Excel excel = excel_lib.Excel.createExcel();
      final sheet = excel['Sheet1'];

      // إضافة رؤوس الأعمدة
      final visibleColumns = widget.table.columns.where((col) => col.isVisibleInList).toList();
      for (int i = 0; i < visibleColumns.length; i++) {
        final column = visibleColumns[i];
        final cell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = excel_lib.TextCellValue(column.displayName ?? column.name);

        // تنسيق رأس العمود
        cell.cellStyle = excel_lib.CellStyle(
          bold: true,
          backgroundColorHex: excel_lib.ExcelColor.blue,
          fontColorHex: excel_lib.ExcelColor.white,
        );
      }

      // إضافة بيانات الصفوف
      for (int rowIndex = 0; rowIndex < widget.controller.tableData.length; rowIndex++) {
        final rowData = widget.controller.tableData[rowIndex];
        for (int colIndex = 0; colIndex < visibleColumns.length; colIndex++) {
          final column = visibleColumns[colIndex];
          final cell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 1
          ));

          final value = rowData[column.name];
          if (value != null) {
            // تحديد نوع البيانات
            if (value is num) {
              cell.value = excel_lib.DoubleCellValue(value.toDouble());
            } else if (value is bool) {
              cell.value = excel_lib.BoolCellValue(value);
            } else {
              cell.value = excel_lib.TextCellValue(value.toString());
            }
          }
        }
      }

      // حفظ الملف
      final bytes = excel.encode();
      if (bytes != null) {
        final fileName = '${widget.table.name}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
        await FileSaver.instance.saveFile(
          name: fileName,
          bytes: Uint8List.fromList(bytes),
          ext: 'xlsx',
          mimeType: MimeType.microsoftExcel,
        );
      }

      // إغلاق مؤشر التحميل
      Get.back();

      Get.snackbar(
        'تصدير البيانات',
        'تم تصدير ${widget.controller.tableData.length} سجل إلى ملف Excel بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير البيانات إلى Excel: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// استيراد البيانات من ملف CSV
  void _importFromCsv() async {
    try {
      // اختيار ملف CSV
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        return; // المستخدم ألغى العملية
      }

      final file = result.files.first;
      if (file.bytes == null) {
        Get.snackbar(
          'خطأ',
          'لا يمكن قراءة الملف المحدد',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // عرض مربع حوار لتأكيد الاستيراد
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('تأكيد الاستيراد'),
          content: Text(
            'سيتم استيراد البيانات من الملف "${file.name}". هل أنت متأكد من المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('استيراد'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // قراءة وتحليل ملف CSV
      final csvString = utf8.decode(file.bytes!);
      final csvData = const CsvToListConverter().convert(csvString);

      if (csvData.isEmpty) {
        Get.back(); // إغلاق مؤشر التحميل
        Get.snackbar(
          'خطأ',
          'الملف فارغ أو لا يحتوي على بيانات صالحة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // الصف الأول يحتوي على رؤوس الأعمدة
      final headers = csvData.first.map((e) => e.toString()).toList();
      final dataRows = csvData.skip(1).toList();

      // تحويل البيانات إلى تنسيق مناسب للإرسال إلى API
      final importedRecords = <Map<String, dynamic>>[];

      for (final row in dataRows) {
        final record = <String, dynamic>{};
        for (int i = 0; i < headers.length && i < row.length; i++) {
          final columnName = headers[i];
          final value = row[i];

          // البحث عن العمود المقابل في الجدول
          final column = widget.table.columns.firstWhere(
            (col) => col.displayName == columnName || col.name == columnName,
            orElse: () => DatabaseColumn(name: columnName, type: DatabaseColumnType.text),
          );

          // تحويل القيمة حسب نوع العمود
          dynamic convertedValue = value;
          if (value != null && value.toString().isNotEmpty) {
            switch (column.type) {
              case DatabaseColumnType.integer:
                convertedValue = int.tryParse(value.toString()) ?? 0;
                break;
              case DatabaseColumnType.decimal:
              case DatabaseColumnType.real:
                convertedValue = double.tryParse(value.toString()) ?? 0.0;
                break;
              case DatabaseColumnType.boolean:
                convertedValue = value.toString().toLowerCase() == 'true' || value.toString() == '1';
                break;
              default:
                convertedValue = value.toString();
            }
          }

          record[column.name] = convertedValue;
        }

        if (record.isNotEmpty) {
          importedRecords.add(record);
        }
      }

      // استيراد السجلات واحداً تلو الآخر
      int successCount = 0;
      int errorCount = 0;

      for (final record in importedRecords) {
        try {
          final success = await widget.controller.createRecord(record);
          if (success) {
            successCount++;
          } else {
            errorCount++;
          }
        } catch (e) {
          errorCount++;
          debugPrint('خطأ في استيراد السجل: $e');
        }
      }

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض نتائج الاستيراد
      Get.snackbar(
        'نتائج الاستيراد',
        'تم استيراد $successCount سجل بنجاح${errorCount > 0 ? '، فشل في استيراد $errorCount سجل' : ''}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: successCount > 0 ? Colors.green : Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );

      // إعادة تحميل البيانات
      if (successCount > 0) {
        await widget.controller.loadTableData();
      }

    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في الاستيراد',
        'حدث خطأ أثناء استيراد البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return Colors.blue;
    } catch (e) {
      return Colors.grey;
    }
  }
}
